export const homeList = [
  {
    title: 'New',
    filter: '',
  },
  {
    title: 'Netflix',
    filter: 'web-series/netflix',
  },
  {
    title: 'Amazon Prime',
    filter: 'web-series/amazon-prime-video',
  },
  {
    title: '4K Movies',
    filter: 'movies-by-quality/2160p',
  },
];

export const genresList = [
  {
    title: 'Action',
    filter: 'category/movies-by-genres/action',
  },
  {
    title: 'Adventure',
    filter: 'category/movies-by-genres/adventure',
  },
  {
    title: 'Animation',
    filter: 'category/movies-by-genres/animation',
  },
  {
    title: 'Biography',
    filter: 'category/movies-by-genres/biography',
  },
  {
    title: 'Comedy',
    filter: 'category/movies-by-genres/comedy',
  },
  {
    title: 'Crime',
    filter: 'category/movies-by-genres/crime',
  },
  {
    title: 'Documentary',
    filter: 'category/movies-by-genres/documentary',
  },
  {
    title: 'Drama',
    filter: 'category/movies-by-genres/drama',
  },
  {
    title: 'Family',
    filter: 'category/movies-by-genres/family',
  },
  {
    title: 'Fantasy',
    filter: 'category/movies-by-genres/fantasy',
  },
  {
    title: 'History',
    filter: 'category/movies-by-genres/history',
  },
  {
    title: 'Horror',
    filter: 'category/movies-by-genres/horror',
  },
  {
    title: 'Music',
    filter: 'category/movies-by-genres/music',
  },
  {
    title: 'Mystery',
    filter: 'category/movies-by-genres/mystery',
  },
  {
    title: 'Romance',
    filter: 'category/movies-by-genres/romance',
  },
  {
    title: 'Sci-Fi',
    filter: 'category/movies-by-genres/sci-fi',
  },
  {
    title: 'Sport',
    filter: 'category/movies-by-genres/sport',
  },
  {
    title: 'Thriller',
    filter: 'category/movies-by-genres/thriller',
  },
  {
    title: 'War',
    filter: 'category/movies-by-genres/war',
  },
  {
    title: 'Western',
    filter: 'category/movies-by-genres/western',
  },
];
