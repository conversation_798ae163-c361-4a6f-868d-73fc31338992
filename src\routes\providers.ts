import { Router, Request, Response } from "express";
import { providerService } from "../services/providerService";
import { asyncHandler } from "../middleware/errorHandler";
import {
    validateProvider,
    validateSearchQuery,
    validateInfoQuery,
    validateStreamQuery,
    validateEpisodesQuery,
    validateHomepageQuery,
} from "../utils/validation";
import { generalLimiter, searchLimiter, streamLimiter } from "../middleware/rateLimiter";

const router = Router();

/**
 * GET /api/providers
 * Get list of all available providers
 */
router.get(
    "/",
    generalLimiter,
    asyncHandler(async (req: Request, res: Response) => {
        const providers = await providerService.getProviders();

        res.json({
            success: true,
            data: providers,
            total: providers.length,
        });
    })
);

/**
 * GET /api/homepage/:provider
 * Get homepage data for a specific provider
 */
router.get(
    "/homepage/:provider",
    generalLimiter,
    validateProvider,
    validateHomepage<PERSON><PERSON>y,
    asyncHandler(async (req: Request, res: Response) => {
        const { provider } = req.params;

        const data = await providerService.getHomepage(provider);

        res.json({
            success: true,
            data,
            provider,
        });
    })
);

/**
 * GET /api/search/:provider
 * Search content in a specific provider
 */
router.get(
    "/search/:provider",
    searchLimiter,
    validateProvider,
    validateSearchQuery,
    asyncHandler(async (req: Request, res: Response) => {
        const { provider } = req.params;
        const { q: query, page = "1" } = req.query;

        const data = await providerService.searchContent(provider, query as string, parseInt(page as string, 10));

        res.json({
            success: true,
            data,
            provider,
            query,
            page: parseInt(page as string, 10),
            total: data.length,
        });
    })
);

/**
 * GET /api/info/:provider
 * Get metadata for content
 */
router.get(
    "/info/:provider",
    generalLimiter,
    validateProvider,
    validateInfoQuery,
    asyncHandler(async (req: Request, res: Response) => {
        const { provider } = req.params;
        const { link } = req.query;

        const data = await providerService.getContentInfo(provider, link as string);

        res.json({
            success: true,
            data,
            provider,
        });
    })
);

/**
 * GET /api/stream/:provider
 * Get streaming links
 */
router.get(
    "/stream/:provider",
    streamLimiter,
    validateProvider,
    validateStreamQuery,
    asyncHandler(async (req: Request, res: Response) => {
        const { provider } = req.params;
        const { id, type } = req.query;

        const data = await providerService.getStreamingLinks(provider, id as string, type as string);

        res.json({
            success: true,
            data,
            provider,
            id,
            type,
            total: data.length,
        });
    })
);

/**
 * GET /api/episodes/:provider
 * Get episode links for series
 */
router.get(
    "/episodes/:provider",
    generalLimiter,
    validateProvider,
    validateEpisodesQuery,
    asyncHandler(async (req: Request, res: Response) => {
        const { provider } = req.params;
        const { link } = req.query;

        const data = await providerService.getEpisodeLinks(provider, link as string);

        res.json({
            success: true,
            data,
            provider,
            total: data.length,
        });
    })
);

export default router;
