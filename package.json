{"name": "streaming-api-server", "version": "1.0.0", "description": "RESTful API server for streaming content providers", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["streaming", "api", "express", "typescript", "movies", "series"], "author": "", "license": "MIT", "dependencies": {"@types/cheerio": "^0.22.35", "axios": "^1.6.2", "cheerio": "^1.0.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "morgan": "^1.10.0", "node-cache": "^5.1.2", "react-native-quick-base64": "^2.2.0"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/morgan": "^1.9.9", "@types/node": "^20.10.4", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint": "^8.55.0", "jest": "^29.7.0", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}