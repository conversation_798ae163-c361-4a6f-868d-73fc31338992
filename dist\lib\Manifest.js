"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.manifest = void 0;
const dooflix_1 = require("./providers/dooflix");
const autoEmbedDrama_1 = require("./providers/autoEmbedDrama");
const autoEmbedAnime_1 = require("./providers/autoEmbedAnime");
const autoEmbed_1 = require("./providers/autoEmbed");
const toonstream_1 = require("./providers/toonstream");
const ridoMovies_1 = require("./providers/ridoMovies");
const protonMovies_1 = require("./providers/protonMovies");
const ringz_1 = require("./providers/ringz");
const topmovies_1 = require("./providers/topmovies");
const primeMirror_1 = require("./providers/primeMirror");
exports.manifest = {
    autoEmbed: autoEmbed_1.autoEmbed,
    multiStream: autoEmbed_1.autoEmbed,
    dooflix: dooflix_1.dooflixProvider,
    AEDrama: autoEmbedDrama_1.autoEmbedDrama,
    AEAnime: autoEmbedAnime_1.AEAnime,
    toonstream: toonstream_1.toonstream,
    ridoMovies: ridoMovies_1.ridoMovies,
    protonMovies: protonMovies_1.protonMovies,
    ringz: ringz_1.ringz,
    topMovies: topmovies_1.topMovies,
    primeMirror: primeMirror_1.primeMirror,
};
//# sourceMappingURL=Manifest.js.map