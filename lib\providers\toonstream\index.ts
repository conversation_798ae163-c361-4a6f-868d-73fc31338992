import { ProviderType } from "../../Manifest";
import { toonCatalog, toonGenresList } from "./toonCatalog";
import { toonGetInfo } from "./toonGetInfo";
import { toonGetPosts, toonGetPostsSearch } from "./toonGetPosts";
import { toonGetEpisodeLinks } from "./toonGetEpisodes";
import { Stream } from "../types";

// Temporary stream function that returns empty array
const toonGetStream = async (link: string, type: string, signal: AbortSignal): Promise<Stream[]> => {
    return [];
};

export const toonstream: ProviderType = {
    catalog: toonCatalog,
    genres: toonGenresList,
    GetMetaData: toonGetInfo,
    GetHomePosts: toonGetPosts,
    GetStream: toonGetStream,
    GetSearchPosts: toonGetPostsSearch,
    GetEpisodeLinks: toonGetEpisodeLinks,
};
