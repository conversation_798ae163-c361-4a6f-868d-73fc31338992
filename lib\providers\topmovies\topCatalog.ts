export const topCatalogList = [
  {
    title: 'Latest',
    filter: '',
  },
  {
    title: 'Netflix',
    filter: '/web-series/tv-shows-by-network/netflix',
  },
  {
    title: 'Hotstar',
    filter: '/web-series/tv-shows-by-network/hotstar',
  },
  {
    title: 'Amazon Prime',
    filter: '/web-series/tv-shows-by-network/amazon-prime-video',
  },
];

export const topGenresList = [
  {
    title: 'Apple TV+',
    filter: '/ott/apple-tv',
  },
  {
    title: 'Disney+',
    filter: '/ott/disney-plus',
  },
  {
    title: 'Hulu',
    filter: '/ott/hulu',
  },
  {
    title: 'Crunchyroll',
    filter: '/ott/crunchyroll',
  },
  {
    title: 'Action',
    filter: '/movies-by-genre/action/',
  },
  {
    title: 'Adventure',
    filter: '/movies-by-genre/adventure/',
  },
  {
    title: 'Animation',
    filter: '/movies-by-genre/animated/',
  },
  {
    title: 'Comedy',
    filter: '/movies-by-genre/comedy/',
  },
  {
    title: 'Crime',
    filter: '/movies-by-genre/crime/',
  },
  {
    title: 'Documentary',
    filter: '/movies-by-genre/documentary/',
  },
  {
    title: 'Fantasy',
    filter: '/movies-by-genre/fantasy/',
  },
  {
    title: 'Horror',
    filter: '/movies-by-genre/horror/',
  },
  {
    title: 'Mystery',
    filter: '/movies-by-genre/mystery/',
  },
  {
    title: 'Romance',
    filter: '/movies-by-genre/romance/',
  },
  {
    title: 'Thriller',
    filter: '/movies-by-genre/thriller/',
  },
  {
    title: 'Sci-Fi',
    filter: '/movies-by-genre/sci-fi/',
  },
];
