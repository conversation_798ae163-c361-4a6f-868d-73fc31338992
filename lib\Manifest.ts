import { Post, Stream, Info, EpisodeLink, Catalog } from "./providers/types";

/// Working Providers (without React Native dependencies)
import { dooflixProvider } from "./providers/dooflix";
import { autoEmbedDrama } from "./providers/autoEmbedDrama";
import { AEAnime } from "./providers/autoEmbedAnime";
import { toonstream } from "./providers/toonstream";
import { ridoMovies } from "./providers/ridoMovies";
import { protonMovies } from "./providers/protonMovies";
import { ringz } from "./providers/ringz";
import { topMovies } from "./providers/topmovies";
import { primeMirror } from "./providers/primeMirror";

export interface ProviderType {
    searchFilter?: string;
    catalog: Catalog[];
    genres: Catalog[];
    blurImage?: boolean;
    nonStreamableServer?: string[];
    nonDownloadableServer?: string[];
    GetStream: (link: string, type: string, signal: AbortSignal) => Promise<Stream[]>;
    GetHomePosts: (filter: string, page: number, provider: string, signal: AbortSignal) => Promise<Post[]>;
    GetEpisodeLinks?: (url: string) => Promise<EpisodeLink[]>;
    GetMetaData: (link: string, provider: string) => Promise<Info>;
    GetSearchPosts: (searchQuery: string, page: number, provider: string, signal: AbortSignal) => Promise<Post[]>;
}
export interface Manifest {
    [key: string]: ProviderType;
}

export const manifest: Manifest = {
    // Basic working providers for initial testing
    dooflix: dooflixProvider,
    AEDrama: autoEmbedDrama,
    AEAnime: AEAnime,
    toonstream: toonstream,
    ridoMovies: ridoMovies,
    protonMovies: protonMovies,
    ringz: ringz,
    topMovies: topMovies,
    primeMirror: primeMirror,

    // Temporarily disabled due to React Native dependencies
    // autoEmbed: autoEmbed,
    // multiStream: autoEmbed,
};
