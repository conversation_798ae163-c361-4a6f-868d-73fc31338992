"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toonstream = void 0;
const toonCatalog_1 = require("./toonCatalog");
const toonGetInfo_1 = require("./toonGetInfo");
const toonGetPosts_1 = require("./toonGetPosts");
const toonGetEpisodes_1 = require("./toonGetEpisodes");
const toonGetStream = async (link, type, signal) => {
    return [];
};
exports.toonstream = {
    catalog: toonCatalog_1.toonCatalog,
    genres: toonCatalog_1.toonGenresList,
    GetMetaData: toonGetInfo_1.toonGetInfo,
    GetHomePosts: toonGetPosts_1.toonGetPosts,
    GetStream: toonGetStream,
    GetSearchPosts: toonGetPosts_1.toonGetPostsSearch,
    GetEpisodeLinks: toonGetEpisodes_1.toonGetEpisodeLinks,
};
//# sourceMappingURL=index.js.map