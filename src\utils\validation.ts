import { body, param, query, validationResult } from "express-validator";
import { Request, Response, NextFunction } from "express";
import { manifest } from "../../lib/Manifest";

/**
 * Middleware to handle validation errors
 */
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({
            success: false,
            error: "Validation failed",
            details: errors.array(),
        });
        return;
    }
    next();
};

/**
 * Validate provider parameter
 */
export const validateProvider = [
    param("provider")
        .isString()
        .notEmpty()
        .custom((value) => {
            if (!manifest[value]) {
                throw new Error(`Provider '${value}' not found`);
            }
            return true;
        })
        .withMessage("Invalid provider"),
    handleValidationErrors,
];

/**
 * Validate search query parameters
 */
export const validateSearchQuery = [
    query("q").isString().isLength({ min: 1, max: 100 }).withMessage("Search query must be between 1 and 100 characters"),
    query("page").optional().isInt({ min: 1, max: 50 }).withMessage("Page must be a number between 1 and 50"),
    handleValidationErrors,
];

/**
 * Validate info query parameters
 */
export const validateInfoQuery = [query("link").isString().isURL().withMessage("Link must be a valid URL"), handleValidationErrors];

/**
 * Validate stream query parameters
 */
export const validateStreamQuery = [
    query("id").isString().notEmpty().withMessage("ID is required"),
    query("type").isString().isIn(["movie", "series"]).withMessage('Type must be either "movie" or "series"'),
    handleValidationErrors,
];

/**
 * Validate episodes query parameters
 */
export const validateEpisodesQuery = [query("link").isString().isURL().withMessage("Link must be a valid URL"), handleValidationErrors];

/**
 * Validate homepage query parameters
 */
export const validateHomepageQuery = [
    query("filter").optional().isString().withMessage("Filter must be a string"),
    query("page").optional().isInt({ min: 1, max: 10 }).withMessage("Page must be a number between 1 and 10"),
    handleValidationErrors,
];

/**
 * Get available providers list
 */
export const getAvailableProviders = (): string[] => {
    return Object.keys(manifest);
};
