"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAvailableProviders = exports.validateHomepageQuery = exports.validateEpisodesQuery = exports.validateStreamQuery = exports.validateInfoQuery = exports.validateSearchQuery = exports.validateProvider = exports.handleValidationErrors = void 0;
const express_validator_1 = require("express-validator");
const Manifest_1 = require("../../lib/Manifest");
const handleValidationErrors = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        res.status(400).json({
            success: false,
            error: "Validation failed",
            details: errors.array(),
        });
        return;
    }
    next();
};
exports.handleValidationErrors = handleValidationErrors;
exports.validateProvider = [
    (0, express_validator_1.param)("provider")
        .isString()
        .notEmpty()
        .custom((value) => {
        if (!Manifest_1.manifest[value]) {
            throw new Error(`Provider '${value}' not found`);
        }
        return true;
    })
        .withMessage("Invalid provider"),
    exports.handleValidationErrors,
];
exports.validateSearchQuery = [
    (0, express_validator_1.query)("q").isString().isLength({ min: 1, max: 100 }).withMessage("Search query must be between 1 and 100 characters"),
    (0, express_validator_1.query)("page").optional().isInt({ min: 1, max: 50 }).withMessage("Page must be a number between 1 and 50"),
    exports.handleValidationErrors,
];
exports.validateInfoQuery = [(0, express_validator_1.query)("link").isString().isURL().withMessage("Link must be a valid URL"), exports.handleValidationErrors];
exports.validateStreamQuery = [
    (0, express_validator_1.query)("id").isString().notEmpty().withMessage("ID is required"),
    (0, express_validator_1.query)("type").isString().isIn(["movie", "series"]).withMessage('Type must be either "movie" or "series"'),
    exports.handleValidationErrors,
];
exports.validateEpisodesQuery = [(0, express_validator_1.query)("link").isString().isURL().withMessage("Link must be a valid URL"), exports.handleValidationErrors];
exports.validateHomepageQuery = [
    (0, express_validator_1.query)("filter").optional().isString().withMessage("Filter must be a string"),
    (0, express_validator_1.query)("page").optional().isInt({ min: 1, max: 10 }).withMessage("Page must be a number between 1 and 10"),
    exports.handleValidationErrors,
];
const getAvailableProviders = () => {
    return Object.keys(Manifest_1.manifest);
};
exports.getAvailableProviders = getAvailableProviders;
//# sourceMappingURL=validation.js.map