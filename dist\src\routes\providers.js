"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const providerService_1 = require("../services/providerService");
const errorHandler_1 = require("../middleware/errorHandler");
const validation_1 = require("../utils/validation");
const rateLimiter_1 = require("../middleware/rateLimiter");
const router = (0, express_1.Router)();
router.get("/", rateLimiter_1.generalLimiter, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const providers = await providerService_1.providerService.getProviders();
    res.json({
        success: true,
        data: providers,
        total: providers.length,
    });
}));
router.get("/homepage/:provider", rateLimiter_1.generalLimiter, validation_1.validateProvider, validation_1.validateHomepageQuery, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { provider } = req.params;
    const data = await providerService_1.providerService.getHomepage(provider);
    res.json({
        success: true,
        data,
        provider,
    });
}));
router.get("/search/:provider", rateLimiter_1.searchLimiter, validation_1.validateProvider, validation_1.validateSearchQuery, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { provider } = req.params;
    const { q: query, page = "1" } = req.query;
    const data = await providerService_1.providerService.searchContent(provider, query, parseInt(page, 10));
    res.json({
        success: true,
        data,
        provider,
        query,
        page: parseInt(page, 10),
        total: data.length,
    });
}));
router.get("/info/:provider", rateLimiter_1.generalLimiter, validation_1.validateProvider, validation_1.validateInfoQuery, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { provider } = req.params;
    const { link } = req.query;
    const data = await providerService_1.providerService.getContentInfo(provider, link);
    res.json({
        success: true,
        data,
        provider,
    });
}));
router.get("/stream/:provider", rateLimiter_1.streamLimiter, validation_1.validateProvider, validation_1.validateStreamQuery, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { provider } = req.params;
    const { id, type } = req.query;
    const data = await providerService_1.providerService.getStreamingLinks(provider, id, type);
    res.json({
        success: true,
        data,
        provider,
        id,
        type,
        total: data.length,
    });
}));
router.get("/episodes/:provider", rateLimiter_1.generalLimiter, validation_1.validateProvider, validation_1.validateEpisodesQuery, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { provider } = req.params;
    const { link } = req.query;
    const data = await providerService_1.providerService.getEpisodeLinks(provider, link);
    res.json({
        success: true,
        data,
        provider,
        total: data.length,
    });
}));
exports.default = router;
//# sourceMappingURL=providers.js.map